# Refactoring Bank Rabbit Gateway for Transactional Message Processing

## 1. Overview

This document outlines the plan to refactor the Oracle AQ message consumer in the Bank Rabbit Gateway service. The goal is to move from a non-transactional, client-acknowledged model to a fully transactional model. This change is critical to ensure message processing is atomic, prevent data loss, and handle "poison messages" gracefully.

## 2. The Problem: Critical Flaws in the Existing Implementation

Our review of the existing implementation, which uses `Session.CLIENT_ACKNOWLEDGE`, identified two critical flaws that compromise the reliability of the service.

### Flaw 1: The Data Loss Scenario

The most severe issue is a race condition that leads to permanent message loss. It occurs as follows:

1.  **Message A (Bad Message) Arrives:** It is processed, but a non-JMS exception occurs (e.g., a failure to publish to RabbitMQ). The `catch` block logs the error but does **not** acknowledge the message.
2.  **Message B (Good Message) Arrives:** It is processed successfully on the same JMS session.
3.  **`messageB.acknowledge()` is Called:** In `CLIENT_ACKNOWLEDGE` mode, this call acknowledges **all messages delivered on the session so far**.
4.  **Catastrophic Result:** Both Message B **and the failed Message A** are acknowledged and removed from the Oracle AQ queue. Message A was never successfully processed but is now lost forever.

### Flaw 2: The "Poison Message" Infinite Loop

The second issue is the lack of handling for messages that consistently fail to process for reasons other than a JMS connection error (e.g., malformed data).

1.  A "poison message" is received and fails processing.
2.  The error is caught, but the message is not acknowledged.
3.  Oracle AQ's visibility timeout expires, and the message is redelivered.
4.  The service immediately tries to process it again, and it fails again.

This creates a tight loop, flooding logs and preventing any subsequent valid messages in the queue from being processed.

## 3. The Proposed Solution: Manual JMS Transaction Management

To solve these issues, we will switch to the `Session.SESSION_TRANSACTED` mode. This is the simplest and most correct solution.

### Why Not Micronaut's `@Transactional`?

We considered using Micronaut's `@Transactional` annotation, but it is not a viable solution here. That annotation manages **JDBC transactions**, not JMS transactions. The two are independent unless a complex JTA (Java Transaction API) provider is configured, which is overkill and violates the goal of finding the simplest solution.

### The Chosen Approach: `SESSION_TRANSACTED`

By using transacted sessions, we gain atomic, per-message control directly from the JMS API.

- **On Success:** We will call `session.commit()`. This atomically acknowledges the message and removes it from the queue.
- **On Failure:** We will call `session.rollback()`. This discards the processing attempt and makes the message immediately available for redelivery.

This approach completely isolates message processing, eliminating the risk of one message's state affecting another.

## 4. Detailed Implementation Plan

The implementation will be done in three main steps.

### Step 1: Configure the JMS Session for Transactions

The foundation of the solution is to create a transacted session.

- **File:** `service/src/main/kotlin/cz/partners/bank/rabbitgateway/service/OracleAqJmsService.kt`
- **Action:** Change the session creation parameters from non-transacted to transacted.

**Before:**
```kotlin
jmsSession = jmsConnection.createQueueSession(false, Session.CLIENT_ACKNOWLEDGE) as AQjmsSession
```

**After:**
```kotlin
jmsSession = jmsConnection.createQueueSession(true, Session.SESSION_TRANSACTED) as AQjmsSession
```

### Step 2: Refactor Message Processing to be Atomic

We will modify the core message processing logic to use `commit` instead of `acknowledge`.

- **File:** `service/src/main/kotlin/cz/partners/bank/rabbitgateway/service/impl/MessageRelayCommonServiceImpl.kt`
- **Action:** Rework the `processMessage` function to get the session and commit the transaction upon success.

**Before:**
```kotlin
private fun processMessage(message: Message, instance: String) {
    try {
        logger.debug("[$instance] Received message from queue [$queueName]: ${message.jmsMessageID}")

        if (message is TextMessage) {
            val messageText = message.text
            logger.debug("[$instance] Received AQ JMS message from [$queueName]: $messageText")
            incomingProducer.publish(IncomingMessage(messageText))
            message.acknowledge()
        } else {
            logger.warn("[$instance] Received non-text message from [$queueName]: ${message.jmsMessageID}, skipping")
            message.acknowledge() // Still acknowledge non-text messages to avoid blocking the queue
        }
    } catch (e: Exception) {
        handleMessageProcessingError(message, instance, e)
    }
}
```

**After:**
```kotlin
private fun processMessage(message: Message, instance: String) {
    // Get the session directly from the message. This is guaranteed to be an AQjmsSession.
    val session = (message as? AQjmsMessage)?.session 
        ?: throw IllegalStateException("Session is not an AQjmsSession or is null")

    try {
        // Poison message handling logic will be here (see Step 3)
        
        logger.debug("[$instance] Received message from queue [$queueName]: ${message.jmsMessageID}")

        if (message is TextMessage) {
            val messageText = message.text
            logger.debug("[$instance] Received AQ JMS message from [$queueName]: $messageText")
            incomingProducer.publish(IncomingMessage(messageText))
            session.commit() // Commit the transaction on success
        } else {
            logger.warn("[$instance] Received non-text message from [$queueName]: ${message.jmsMessageID}, skipping")
            session.commit() // Commit to discard non-text messages
        }
    } catch (e: Exception) {
        handleMessageProcessingError(session, message, instance, e)
    }
}
```

### Step 3: Implement Robust Error Handling with Rollbacks

Finally, we will implement the rollback logic and the poison message handling.

- **File:** `service/src/main/kotlin/cz/partners/bank/rabbitgateway/service/impl/MessageRelayCommonServiceImpl.kt`
- **Action:** Add a `MAX_RETRIES` constant and completely refactor `handleMessageProcessingError` and `processMessage` to manage rollbacks and poison messages.

**The full, final implementation of `processMessage` and `handleMessageProcessingError` will be:**

```kotlin
private val MAX_RETRIES = 3 // Max number of processing attempts

private fun processMessage(message: Message, instance: String) {
    val session = (message as? AQjmsMessage)?.session
        ?: throw IllegalStateException("Session is not an AQjmsSession or is null")

    try {
        // 1. Check for poison messages
        val deliveryCount = message.getIntProperty("JMSXDeliveryCount")
        if (deliveryCount > MAX_RETRIES) {
            logger.error("[$instance] Message [${message.jmsMessageID}] has failed processing $deliveryCount times. Discarding as a poison message. Content: ${(message as? TextMessage)?.text}")
            session.commit() // Commit to permanently discard the message
            return
        }

        // 2. Process the message
        logger.debug("[$instance] Received message from queue [$queueName]: ${message.jmsMessageID} (delivery attempt $deliveryCount)")
        if (message is TextMessage) {
            val messageText = message.text
            incomingProducer.publish(IncomingMessage(messageText))
            session.commit() // 3. Commit on success
        } else {
            logger.warn("[$instance] Received non-text message from [$queueName]: ${message.jmsMessageID}, skipping")
            session.commit() // Commit to discard non-text messages
        }
    } catch (e: Exception) {
        // 4. Handle any exception
        handleMessageProcessingError(session, message, instance, e)
    }
}

private fun handleMessageProcessingError(session: AQjmsSession, message: Message, instance: String, error: Exception) {
    logger.error("[$instance] Error processing message from queue [$queueName]: ${message.jmsMessageID}", error)
    
    // 1. Always roll back the transaction on any processing error
    try {
        session.rollback()
    } catch (rollbackEx: JMSException) {
        logger.error("[$instance] CRITICAL: Failed to rollback session after a processing error. The consumer might be in an inconsistent state.", rollbackEx)
    }

    // 2. If the error was a JMSException, re-throw it to trigger the consumer's main retry logic
    if (error is JMSException) {
        logger.error("[$instance] JMS error detected. Rethrowing to trigger consumer restart.", error)
        throw error
    }
}
```

## 5. Expected Outcome

Upon completion of this refactoring, the service will be significantly more robust:

- **Atomicity:** Each message is processed in an isolated transaction.
- **Data Integrity:** The risk of accidental acknowledgment and message loss is eliminated.
- **Resilience:** The consumer can gracefully handle and discard poison messages without halting.
- **Stability:** The overall stability and reliability of the message gateway will be greatly improved.
