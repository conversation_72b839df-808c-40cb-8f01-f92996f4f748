package cz.partners.bank.rabbitgateway.service

import cz.partners.bank.rabbitgateway.configuration.LooperConfiguration
import cz.partners.bank.rabbitgateway.configuration.LooperHealthIndicator
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator.ACCOUNT_CHANGES
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator.CARD_HOLD
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator.DAILY_CLOSING
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator.OUTGOING_MESSAGE
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator.PLANNED_TRANSACTIONS
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator.REALIZED
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator.RECORD_VERSION_CHANGE
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator.REQUIRED
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator.SUSPECTED_FRAUD
import cz.partners.bank.rabbitgateway.configuration.GlobalSettings
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayAccountChangesServiceImpl
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayDailyClosingServiceImpl
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayHoldServiceImpl
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayOutgoingMessageServiceImpl
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayPlannedTransactionsServiceImpl
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayRealizedServiceImpl
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayRecordVersionChangeServiceImpl
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayRequiredServiceImpl
import cz.partners.bank.rabbitgateway.service.impl.MessageRelaySuspectedFraudServiceImpl
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.ContextProvider
import io.micronaut.context.annotation.Context
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import javax.annotation.PostConstruct

@Context
class RelayLooper(
    private val messageRelayRealizedService: MessageRelayRealizedServiceImpl,
    private val messageRelayRequiredService: MessageRelayRequiredServiceImpl,
    private val messageRelayCardHoldService: MessageRelayHoldServiceImpl,
    private val messageRelayRecordVersionChangeService: MessageRelayRecordVersionChangeServiceImpl,
    private val messageRelayDailyClosingService: MessageRelayDailyClosingServiceImpl,
    private val messageRelaySuspectedFraudService: MessageRelaySuspectedFraudServiceImpl,
    private val messageRelayPlannedTransactionsService: MessageRelayPlannedTransactionsServiceImpl,
    private val messageRelayReducedInterestRateMessage: MessageRelayOutgoingMessageServiceImpl,
    private val messageRelayAccountChangesService: MessageRelayAccountChangesServiceImpl,

    private val looperHealthIndicator: LooperHealthIndicator,
    private val contextProvider: ContextProvider,
    private val looperSettings: GlobalSettings,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @PostConstruct
    private fun loop() {
        logger.info("Starting looper with [${showConfig()}] (use-polling: ${looperSettings.usePolling})")
        looperSettings.getConfigurations().map { looper ->
            initLoop(looper)
        }
    }

    private fun showConfig(): String {
        val s = StringBuilder("\n")
        looperSettings.getConfigurations().map {
            s.append("${it.name}: enabled [${it.enabled}] \n")
        }
        return s.toString()
    }

    private fun initLoop(looper: LooperConfiguration) {
        if (!looper.enabled) {
            logger.info("Disabling relay looper due to looper.${looper.name}.enabled property")
            return
        }
        
        if (looperSettings.usePolling) {
            logger.info("Initializing polling relay looper for ${looper.name}...")
            CoroutineScope(ContextConfiguration.initialContext()).launch(Dispatchers.IO) {
                logger.info("init polling looper [${looper.name}]")
                loopRead(looper.name, looper.timeoutSeconds)
            }
        } else {
            logger.info("Initializing reactive relay consumer for ${looper.name}...")
            initReactiveConsumer(looper.name)
        }
    }
    
    private fun initReactiveConsumer(operationName: String) {
        when (LooperIndicator.byConfigName(operationName)) {
            REQUIRED -> {
                messageRelayRequiredService.startReactiveConsumer(operationName)
            }
            REALIZED -> {
                messageRelayRealizedService.startReactiveConsumer(operationName)
            }
            CARD_HOLD -> {
                messageRelayCardHoldService.startReactiveConsumer(operationName)
            }
            RECORD_VERSION_CHANGE -> {
                messageRelayRecordVersionChangeService.startReactiveConsumer(operationName)
            }
            DAILY_CLOSING -> {
                messageRelayDailyClosingService.startReactiveConsumer(operationName)
            }
            SUSPECTED_FRAUD -> {
                messageRelaySuspectedFraudService.startReactiveConsumer(operationName)
            }
            PLANNED_TRANSACTIONS -> {
                messageRelayPlannedTransactionsService.startReactiveConsumer(operationName)
            }
            OUTGOING_MESSAGE -> {
                messageRelayReducedInterestRateMessage.startReactiveConsumer(operationName)
            }
            ACCOUNT_CHANGES -> {
                messageRelayAccountChangesService.startReactiveConsumer(operationName)
            }
        }.let { }
    }

    private suspend fun loopRead(
        operationName: String,
        timeoutSeconds: Int,
    ) {
        logger.info("Started consumer [$operationName] ...")
        while (true) {
            // we want separate correlationId for each message
            contextProvider.withJobContext(operationName) {
                try {
                    logger.debug("[$operationName] Trying to relay required transaction")
                    when (LooperIndicator.byConfigName(operationName)) {
                        REQUIRED -> {
                            looperHealthIndicator.updateActualAccess(REQUIRED)
                            messageRelayRequiredService.dequeueAndSendToIncomingMessage("$operationName", timeoutSeconds)
                        }

                        REALIZED -> {
                            looperHealthIndicator.updateActualAccess(REALIZED)
                            messageRelayRealizedService.dequeueAndSendToIncomingMessage("$operationName", timeoutSeconds)
                        }

                        CARD_HOLD -> {
                            looperHealthIndicator.updateActualAccess(CARD_HOLD)
                            messageRelayCardHoldService.dequeueAndSendToIncomingMessage("$operationName", timeoutSeconds)
                        }

                        RECORD_VERSION_CHANGE -> {
                            looperHealthIndicator.updateActualAccess(RECORD_VERSION_CHANGE)
                            messageRelayRecordVersionChangeService.dequeueAndSendToIncomingMessage("$operationName", timeoutSeconds)
                        }

                        DAILY_CLOSING -> {
                            looperHealthIndicator.updateActualAccess(DAILY_CLOSING)
                            messageRelayDailyClosingService.dequeueAndSendToIncomingMessage("$operationName", timeoutSeconds)
                        }

                        SUSPECTED_FRAUD -> {
                            looperHealthIndicator.updateActualAccess(SUSPECTED_FRAUD)
                            messageRelaySuspectedFraudService.dequeueAndSendToIncomingMessage("$operationName", timeoutSeconds)
                        }

                        PLANNED_TRANSACTIONS -> {
                            looperHealthIndicator.updateActualAccess(PLANNED_TRANSACTIONS)
                            messageRelayPlannedTransactionsService.dequeueAndSendToIncomingMessage("$operationName", timeoutSeconds)
                        }

                        OUTGOING_MESSAGE -> {
                            looperHealthIndicator.updateActualAccess(OUTGOING_MESSAGE)
                            messageRelayReducedInterestRateMessage.dequeueAndSendToIncomingMessage("$operationName", timeoutSeconds)
                        }

                        ACCOUNT_CHANGES -> {
                            looperHealthIndicator.updateActualAccess(ACCOUNT_CHANGES)
                            messageRelayAccountChangesService.dequeueAndSendToIncomingMessage("$operationName", timeoutSeconds)
                        }
                    }.let { }
                } catch (e: Throwable) {
                    logger.error("Unexpected exception while looping", e)
                }
            }
        }
    }
}
