package cz.partners.bank.rabbitgateway.service.impl

import arrow.core.Either
import arrow.core.right
import com.fasterxml.jackson.databind.ObjectMapper
import cz.partners.bank.rabbitgateway.exception.NoMessageReceivedException
import cz.partners.bank.rabbitgateway.exception.QueueParseException
import cz.partners.bank.rabbitgateway.producer.IncomingMessage
import cz.partners.bank.rabbitgateway.producer.IncomingProducer
import cz.partners.bank.rabbitgateway.service.MessageRelayService
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.exception.PbkException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.slf4j.Logger
import oracle.jms.AQjmsSession
import javax.jms.JMSException
import javax.jms.Message
import javax.jms.TextMessage
import javax.transaction.Transactional

abstract class MessageRelayCommonServiceImpl<TransactionDto>(
    private val incomingProducer: IncomingProducer,
    val objectMapper: ObjectMapper,
    protected val oracleDataSource: OracleDataSource,
    protected val oracleAqJmsService: OracleAqJmsService,
) : MessageRelayService {
    protected abstract val logger: Logger
    
    /**
     * The queue name to consume from
     * Must be overridden by subclasses
     */
    protected abstract val queueName: String
    
    /**
     * Maximum number of retries for poison message handling
     */
    private val MAX_RETRIES = 3

    @Transactional
    override fun dequeueAndSendToIncomingMessage(
        instance: String,
        timeoutSeconds: Int,
    ) {
        dequeueMessage(instance, timeoutSeconds)
            .fold(
                { error ->
                    processException(error)
                },
                { message ->
                    incomingProducer.publish(
                        IncomingMessage(message)
                    )
                }
            )
    }

    /**
     * Starts a reactive consumer for the Oracle AQ JMS queue
     * This method launches a coroutine that listens to the queue indefinitely
     *
     * @param instance The instance identifier for logging
     */
    fun startReactiveConsumer(instance: String) {
        logger.info("[$instance] Starting reactive consumer for queue [$queueName]")

        CoroutineScope(ContextConfiguration.initialContext()).launch(Dispatchers.IO) {
            oracleAqJmsService.listenToQueue(queueName, instance)
                .onEach { (message, session) ->
                    processMessage(message, session, instance)
                }
                .catch { error ->
                    logger.error("[$instance] Error in reactive consumer for queue [$queueName]", error)
                    // Restart the consumer after a short delay
                    kotlinx.coroutines.delay(5000)
                    startReactiveConsumer(instance)
                }
                .collect()
        }
    }

    /**
     * Process a JMS message received from the queue
     */
    private fun processMessage(message: Message, session: AQjmsSession, instance: String) {
        try {
            // Check for poison messages
            val deliveryCount = try {
                message.getIntProperty("JMS_OracleDeliveryCount")
            } catch (e: Exception) {
                // If property doesn't exist, assume first delivery
                1
            }
            
            if (deliveryCount > MAX_RETRIES) {
                logger.error("[$instance] Message [${message.jmsMessageID}] has failed processing $deliveryCount times. Discarding as a poison message. Content: ${(message as? TextMessage)?.text}")
                session.commit() // Commit to permanently discard the message
                return
            }
            
            logger.debug("[$instance] Received message from queue [$queueName]: ${message.jmsMessageID} (delivery attempt $deliveryCount)")

            if (message is TextMessage) {
                val messageText = message.text
                logger.debug("[$instance] Received AQ JMS message from [$queueName]: $messageText")
                incomingProducer.publish(IncomingMessage(messageText))
                session.commit() // Commit the transaction on success
            } else {
                logger.warn("[$instance] Received non-text message from [$queueName]: ${message.jmsMessageID}, skipping")
                session.commit() // Commit to discard non-text messages
            }
        } catch (e: Exception) {
            handleMessageProcessingError(session, message, instance, e)
        }
    }

    /**
     * Handle errors that occur during message processing
     */
    private fun handleMessageProcessingError(session: AQjmsSession, message: Message, instance: String, error: Exception) {
        logger.error("[$instance] Error processing message from queue [$queueName]: ${message.jmsMessageID}", error)
        
        // Always roll back the transaction on any processing error
        try {
            session.rollback()
        } catch (rollbackEx: JMSException) {
            logger.error("[$instance] CRITICAL: Failed to rollback session after a processing error. The consumer might be in an inconsistent state.", rollbackEx)
        }

        // If the error was a JMSException, re-throw it to trigger the consumer's main retry logic
        if (error is JMSException) {
            logger.error("[$instance] JMS error detected. Rethrowing to trigger consumer restart.", error)
            throw error
        }
    }

    override fun relayMessage(
        message: String,
    ): Either<PbkException, Unit> = unmarshalResult(message)
        .map { unmarshalledMessage ->
            produceResult(unmarshalledMessage)
        }

    abstract fun unmarshalResult(rawResult: String): Either<QueueParseException, TransactionDto>

    abstract fun dequeueMessage(instance: String, timeoutSeconds: Int): Either<PbkException, String>

    abstract fun produceResult(message: TransactionDto): Either<PbkException, Unit>

    protected inline fun <reified Type> unmarshalResultHelper(
        rawResult: String,
    ): Either<QueueParseException, Type> {
        return Either.catch {
            objectMapper.readValue(rawResult, Type::class.java)
        }.mapLeft { QueueParseException(rawResult, it) }
    }

    private fun processException(error: Throwable): Either<PbkException, Unit> {
        when (error) {
            is NoMessageReceivedException -> logger.debug("No message received [${error.sourceQueue}] [${error.instance}]")
            else -> {
                logger.error("relay ${this.javaClass} failed", error)
                throw error // to revert transaction
            }
        }
        return Unit.right()
    }
}
