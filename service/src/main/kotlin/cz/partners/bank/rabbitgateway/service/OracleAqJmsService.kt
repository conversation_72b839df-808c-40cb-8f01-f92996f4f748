package cz.partners.bank.rabbitgateway.service

import cz.partners.bank.rabbitgateway.configuration.OracleAqJmsConfiguration
import cz.pbktechnology.platform.common.exception.IoPbkException
import jakarta.annotation.PreDestroy
import jakarta.inject.Singleton
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.retryWhen
import kotlin.math.min
import kotlin.math.pow
import oracle.jms.AQjmsSession
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import javax.jms.Message
import javax.jms.MessageConsumer
import javax.jms.QueueConnection
import javax.jms.Session

/**
 * Service for reactive Oracle AQ JMS integration
 */
@Singleton
open class OracleAqJmsService(
    private val jmsConnection: QueueConnection,
    private val oracleAqJmsConfiguration: OracleAqJmsConfiguration
) {
    private val logger: Logger = LoggerFactory.getLogger(OracleAqJmsService::class.java)
    
    @PreDestroy
    fun cleanup() {
        try {
            logger.info("Closing Oracle AQ JMS connection")
            jmsConnection.close()
        } catch (e: Exception) {
            logger.warn("Error closing Oracle AQ JMS connection during shutdown", e)
        }
    }

    /**
     * Creates a Flow that listens to messages on the specified Oracle AQ queue
     *
     * @param queueName The name of the Oracle AQ queue
     * @param instance Instance identifier for logging
     * @return A Flow that emits messages as they arrive with retry logic
     */
    fun listenToQueue(queueName: String, instance: String): Flow<Pair<Message, AQjmsSession>> {
        return callbackFlow {
            var jmsSession: AQjmsSession? = null
            var consumer: MessageConsumer? = null
            
            try {
                jmsSession = jmsConnection.createQueueSession(true, Session.SESSION_TRANSACTED) as AQjmsSession

                // Get queue & create consumer with schema
                logger.debug("Using Oracle AQ schema: ${oracleAqJmsConfiguration.oracleAqSchema()} for queue: $queueName")
                val queue = jmsSession.getQueue(oracleAqJmsConfiguration.oracleAqSchema(), queueName)
                consumer = jmsSession.createConsumer(queue)

                // Add listener for messages
                val currentSession = jmsSession
                consumer.setMessageListener { message ->
                    logger.debug("[$instance] Received AQ JMS message from [$queueName]: ${message.jmsMessageID}")
                    val result = trySend(Pair(message, currentSession))
                    if (result.isFailure) {
                        logger.warn("[$instance] Failed to send message to flow for queue [$queueName]", result.exceptionOrNull())
                    }
                }

                // On cancel, close only session and consumer resources
                awaitClose {
                    try {
                        logger.debug("[$instance] Disposing resources for queue [$queueName]")
                        consumer?.close()
                        jmsSession.close()
                    } catch (e: Exception) {
                        logger.warn("[$instance] Error closing AQ JMS resources for [$queueName]", e)
                    }
                }
            } catch (e: Exception) {
                logger.error("[$instance] Error setting up AQ JMS listener for [$queueName]", e)
                
                // Clean up resources if setup fails
                try {
                    consumer?.close()
                    jmsSession?.close()
                } catch (closeEx: Exception) {
                    logger.warn("[$instance] Error during cleanup after setup failure for [$queueName]", closeEx)
                }
                
                close(IoPbkException("Error setting up AQ JMS listener", queueName, e))
                return@callbackFlow
            }
        }.retryWhen { cause, attempt ->
            val backoffDelay = min(1000 * (2.0.pow(attempt.toInt())).toLong(), 60000) // Max 60 seconds
            logger.warn("[$instance] Retrying connection to queue [$queueName] after error (attempt ${attempt + 1}), waiting ${backoffDelay}ms", cause)
            delay(backoffDelay)
            true // Always retry indefinitely for all errors
        }
    }
}
