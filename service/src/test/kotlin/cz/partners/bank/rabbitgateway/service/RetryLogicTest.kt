package cz.partners.bank.rabbitgateway.service

import cz.partners.bank.rabbitgateway.producer.IncomingProducer
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayCommonServiceImpl
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.runBlocking
import oracle.jms.AQjmsSession
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import javax.jms.JMSException
import javax.jms.Message
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * Test to verify the improved retry logic architecture
 */
@MicronautTest
class RetryLogicTest {
    
    @MockBean(OracleAqJmsService::class)
    fun mockOracleAqJmsService(): OracleAqJmsService = mockk()
    
    @MockBean(IncomingProducer::class)
    fun mockIncomingProducer(): IncomingProducer = mockk()
    
    /**
     * Test implementation to verify consumer management
     */
    class TestMessageRelayService(
        private val oracleAqJmsService: OracleAqJmsService,
        incomingProducer: IncomingProducer
    ) : MessageRelayCommonServiceImpl<Any>(
        incomingProducer = incomingProducer,
        objectMapper = mockk(),
        oracleDataSource = mockk(),
        oracleAqJmsService = oracleAqJmsService
    ) {
        override val logger: Logger = mockk(relaxed = true)
        override val queueName: String = "TEST_QUEUE"
        
        override fun unmarshalResult(rawResult: String) = throw NotImplementedError()
        override fun dequeueMessage(instance: String, timeoutSeconds: Int) = throw NotImplementedError()
        override fun produceResult(message: Any) = throw NotImplementedError()
        
        // Expose consumer jobs for testing
        fun hasActiveConsumer(instance: String): Boolean {
            return consumerJobs.containsKey(instance)
        }
        
        fun getActiveConsumerCount(): Int {
            return consumerJobs.size
        }
    }
    
    @Test
    fun `should prevent multiple concurrent consumers for same instance`() = runBlocking {
        // Given
        val oracleService = mockk<OracleAqJmsService>()
        val incomingProducer = mockk<IncomingProducer>(relaxed = true)
        
        // Mock a flow that never completes (simulates long-running consumer)
        every { oracleService.listenToQueue(any(), any()) } returns flow<Pair<Message, AQjmsSession>> {
            // Simulate a long-running consumer that doesn't emit
            kotlinx.coroutines.delay(Long.MAX_VALUE)
        }
        
        val service = TestMessageRelayService(oracleService, incomingProducer)
        
        // When
        service.startReactiveConsumer("INSTANCE-1")
        kotlinx.coroutines.delay(100) // Let first consumer start
        
        val countAfterFirst = service.getActiveConsumerCount()
        
        service.startReactiveConsumer("INSTANCE-1") // Start second consumer for same instance
        kotlinx.coroutines.delay(100) // Let second consumer start
        
        val countAfterSecond = service.getActiveConsumerCount()
        
        // Then
        assert(countAfterFirst == 1) { "Should have 1 consumer after first start" }
        assert(countAfterSecond == 1) { "Should still have 1 consumer after second start (first should be cancelled)" }
        
        // Cleanup
        service.stopReactiveConsumer("INSTANCE-1")
    }
    
    @Test
    fun `should allow multiple consumers for different instances`() = runBlocking {
        // Given
        val oracleService = mockk<OracleAqJmsService>()
        val incomingProducer = mockk<IncomingProducer>(relaxed = true)
        
        every { oracleService.listenToQueue(any(), any()) } returns flow<Pair<Message, AQjmsSession>> {
            kotlinx.coroutines.delay(Long.MAX_VALUE)
        }
        
        val service = TestMessageRelayService(oracleService, incomingProducer)
        
        // When
        service.startReactiveConsumer("INSTANCE-1")
        service.startReactiveConsumer("INSTANCE-2")
        kotlinx.coroutines.delay(100)
        
        // Then
        assert(service.getActiveConsumerCount() == 2) { "Should have 2 consumers for different instances" }
        assertTrue(service.hasActiveConsumer("INSTANCE-1"))
        assertTrue(service.hasActiveConsumer("INSTANCE-2"))
        
        // Cleanup
        service.stopReactiveConsumer("INSTANCE-1")
        service.stopReactiveConsumer("INSTANCE-2")
    }
    
    @Test
    fun `should properly cleanup consumer on stop`() = runBlocking {
        // Given
        val oracleService = mockk<OracleAqJmsService>()
        val incomingProducer = mockk<IncomingProducer>(relaxed = true)
        
        every { oracleService.listenToQueue(any(), any()) } returns flow<Pair<Message, AQjmsSession>> {
            kotlinx.coroutines.delay(Long.MAX_VALUE)
        }
        
        val service = TestMessageRelayService(oracleService, incomingProducer)
        
        // When
        service.startReactiveConsumer("INSTANCE-1")
        kotlinx.coroutines.delay(100)
        assertTrue(service.hasActiveConsumer("INSTANCE-1"))
        
        service.stopReactiveConsumer("INSTANCE-1")
        kotlinx.coroutines.delay(100)
        
        // Then
        assertFalse(service.hasActiveConsumer("INSTANCE-1"))
        assert(service.getActiveConsumerCount() == 0)
    }
}