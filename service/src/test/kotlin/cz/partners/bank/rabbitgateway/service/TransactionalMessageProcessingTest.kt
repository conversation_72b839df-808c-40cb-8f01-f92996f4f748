package cz.partners.bank.rabbitgateway.service

import cz.partners.bank.rabbitgateway.producer.IncomingMessage
import cz.partners.bank.rabbitgateway.producer.IncomingProducer
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayCommonServiceImpl
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import jakarta.inject.Inject
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.runBlocking
import oracle.jms.AQjmsMessage
import oracle.jms.AQjmsSession
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import javax.jms.JMSException
import javax.jms.TextMessage

/**
 * Test to verify transactional message processing behavior
 */
@MicronautTest
class TransactionalMessageProcessingTest {
    
    @Inject
    lateinit var oracleAqJmsService: OracleAqJmsService
    
    @Inject
    lateinit var incomingProducer: IncomingProducer
    
    @MockBean(OracleAqJmsService::class)
    fun mockOracleAqJmsService(): OracleAqJmsService = mockk()
    
    @MockBean(IncomingProducer::class)
    fun mockIncomingProducer(): IncomingProducer = mockk()
    
    /**
     * Test implementation of MessageRelayCommonServiceImpl for testing
     */
    class TestMessageRelayService(
        incomingProducer: IncomingProducer,
        oracleDataSource: OracleDataSource,
        oracleAqJmsService: OracleAqJmsService
    ) : MessageRelayCommonServiceImpl<Any>(
        incomingProducer = incomingProducer,
        objectMapper = mockk(),
        oracleDataSource = oracleDataSource,
        oracleAqJmsService = oracleAqJmsService
    ) {
        override val logger: Logger = mockk(relaxed = true)
        override val queueName: String = "TEST_QUEUE"
        
        override fun unmarshalResult(rawResult: String) = throw NotImplementedError()
        override fun dequeueMessage(instance: String, timeoutSeconds: Int) = throw NotImplementedError()
        override fun produceResult(message: Any) = throw NotImplementedError()
    }
    
    @Test
    fun `should commit transaction on successful message processing`() = runBlocking {
        // Given
        val session = mockk<AQjmsSession>(relaxed = true)
        val message = mockk<AQjmsMessage> {
            every { jmsMessageID } returns "MSG-123"
            every { attempts } returns 1
        }
        every { message is TextMessage } returns true
        every { (message as TextMessage).text } returns "test message"
        
        every { oracleAqJmsService.listenToQueue(any(), any()) } returns flowOf(message to session)
        every { incomingProducer.publish(any()) } returns Unit
        
        // When
        val service = TestMessageRelayService(incomingProducer, mockk(), oracleAqJmsService)
        service.startReactiveConsumer("TEST-1")
        
        // Allow coroutine to process
        kotlinx.coroutines.delay(100)
        
        // Then
        verify(exactly = 1) { session.commit() }
        verify(exactly = 0) { session.rollback() }
        verify(exactly = 1) { incomingProducer.publish(IncomingMessage("test message")) }
    }
    
    @Test
    fun `should rollback transaction on message processing failure`() = runBlocking {
        // Given
        val session = mockk<AQjmsSession>(relaxed = true)
        val message = mockk<AQjmsMessage> {
            every { jmsMessageID } returns "MSG-123"
            every { attempts } returns 1
        }
        every { message is TextMessage } returns true
        every { (message as TextMessage).text } returns "test message"
        
        every { oracleAqJmsService.listenToQueue(any(), any()) } returns flowOf(message to session)
        every { incomingProducer.publish(any()) } throws RuntimeException("Processing failed")
        
        // When
        val service = TestMessageRelayService(incomingProducer, mockk(), oracleAqJmsService)
        service.startReactiveConsumer("TEST-1")
        
        // Allow coroutine to process
        kotlinx.coroutines.delay(100)
        
        // Then
        verify(exactly = 0) { session.commit() }
        verify(exactly = 1) { session.rollback() }
    }
    
    @Test
    fun `should discard poison message after max retries`() = runBlocking {
        // Given
        val session = mockk<AQjmsSession>(relaxed = true)
        val message = mockk<AQjmsMessage> {
            every { jmsMessageID } returns "MSG-123"
            every { attempts } returns 4 // More than MAX_RETRIES (3)
        }
        every { message is TextMessage } returns true
        every { (message as TextMessage).text } returns "poison message"
        
        every { oracleAqJmsService.listenToQueue(any(), any()) } returns flowOf(message to session)
        
        // When
        val service = TestMessageRelayService(incomingProducer, mockk(), oracleAqJmsService)
        service.startReactiveConsumer("TEST-1")
        
        // Allow coroutine to process
        kotlinx.coroutines.delay(100)
        
        // Then
        verify(exactly = 1) { session.commit() } // Message should be committed to discard
        verify(exactly = 0) { session.rollback() }
        verify(exactly = 0) { incomingProducer.publish(any()) } // Should not attempt to process
    }
    
    @Test
    fun `should rethrow JMSException after rollback`() = runBlocking {
        // Given
        val session = mockk<AQjmsSession>(relaxed = true)
        val message = mockk<AQjmsMessage> {
            every { jmsMessageID } returns "MSG-123"
            every { attempts } returns 1
        }
        every { message is TextMessage } returns true
        every { (message as TextMessage).text } returns "test message"
        
        val jmsException = JMSException("Connection lost")
        every { oracleAqJmsService.listenToQueue(any(), any()) } returns flowOf(message to session)
        every { incomingProducer.publish(any()) } throws jmsException
        
        // When
        val service = TestMessageRelayService(incomingProducer, mockk(), oracleAqJmsService)
        service.startReactiveConsumer("TEST-1")
        
        // Allow coroutine to process
        kotlinx.coroutines.delay(100)
        
        // Then
        verify(exactly = 0) { session.commit() }
        verify(exactly = 1) { session.rollback() }
        // JMSException should be rethrown to trigger reconnection
    }
}