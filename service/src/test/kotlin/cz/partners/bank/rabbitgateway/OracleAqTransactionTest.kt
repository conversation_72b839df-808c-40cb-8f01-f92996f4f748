package cz.partners.bank.rabbitgateway

import oracle.jms.AQjmsFactory
import oracle.jms.AQjmsMessage
import oracle.jms.AQjmsSession
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import java.util.Properties
import javax.jms.Message
import javax.jms.Session
import javax.jms.TextMessage

/**
 * Test to verify Oracle AQ JMS behavior for transaction refactoring
 * This test is disabled by default as it requires Oracle database connection
 */
@Disabled("Manual test - requires Oracle database")
class OracleAqTransactionTest {
    
    @Test
    fun `verify Oracle AQ message properties and session access`() {
        // This test would connect to Oracle and verify:
        // 1. Can we cast Message to AQjmsMessage?
        // 2. Can we access session from AQjmsMessage?
        // 3. What property name is used for delivery count?
        // 4. Does SESSION_TRANSACTED mode work as expected?
        
        // Example verification code (would need actual Oracle connection):
        /*
        val props = Properties().apply {
            put("user", "username")
            put("password", "password")
        }
        val factory = AQjmsFactory.getQueueConnectionFactory("***********************************", props)
        val connection = factory.createQueueConnection()
        
        // Test 1: SESSION_TRANSACTED mode
        val session = connection.createQueueSession(true, Session.SESSION_TRANSACTED) as AQjmsSession
        
        // Test 2: Message properties
        val queue = session.getQueue("SCHEMA", "QUEUE_NAME")
        val consumer = session.createConsumer(queue)
        connection.start()
        
        val message = consumer.receive(1000)
        if (message != null) {
            // Test casting to AQjmsMessage
            val aqMessage = message as? AQjmsMessage
            println("Can cast to AQjmsMessage: ${aqMessage != null}")
            
            // Test session access
            if (aqMessage != null) {
                val messageSession = aqMessage.session
                println("Can access session from message: ${messageSession != null}")
            }
            
            // Test delivery count properties
            val propertyNames = message.propertyNames
            while (propertyNames.hasMoreElements()) {
                val propName = propertyNames.nextElement() as String
                if (propName.contains("Delivery") || propName.contains("Count")) {
                    println("Found property: $propName = ${message.getObjectProperty(propName)}")
                }
            }
            
            // Test JMSXDeliveryCount
            try {
                val deliveryCount = message.getIntProperty("JMSXDeliveryCount")
                println("JMSXDeliveryCount: $deliveryCount")
            } catch (e: Exception) {
                println("JMSXDeliveryCount not available: ${e.message}")
            }
            
            // Test Oracle-specific property
            try {
                val oracleDeliveryCount = message.getIntProperty("JMS_OracleDeliveryCount")
                println("JMS_OracleDeliveryCount: $oracleDeliveryCount")
            } catch (e: Exception) {
                println("JMS_OracleDeliveryCount not available: ${e.message}")
            }
        }
        
        session.close()
        connection.close()
        */
    }
    
    @Test
    fun `verify SESSION_TRANSACTED commit and rollback behavior`() {
        // This test would verify:
        // 1. commit() acknowledges the message
        // 2. rollback() makes message available for redelivery
        // 3. Each message is handled in isolation
    }
}